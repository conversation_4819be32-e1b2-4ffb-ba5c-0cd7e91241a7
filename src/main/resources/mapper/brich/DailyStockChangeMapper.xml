<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.brich.mapper.DailyStockChangeMapper">
    <select id="findAggregatedChangesByDay" resultType="com.example.brich.model.AggregatedChangeDto">
        SELECT
        trade_date       AS tradeDate,
        stock_code       AS stockCode,
        stock_name       AS stockName,
        change_type      AS changeType,
        total_price      AS totalPrice,
        changer_name      AS changerName,
        changer_position AS changerPosition
        FROM daily_stock_change
        WHERE 1=1
        <if test="changeType != null">
            AND change_type = #{changeType}
        </if>
        AND trade_date BETWEEN #{start} AND #{end}
        ORDER BY trade_date DESC
    </select>

    <select id="findPricePoints" resultType="com.example.brich.model.PricePointDto">
        SELECT
        track_time      AS trackTime,
        current_price   AS currentPrice
        FROM stock_price_tracking
        WHERE stock_code = #{code}
        ORDER BY track_time
    </select>

    <select id="findMarks" resultType="com.example.brich.model.AggregatedChangeDto">
        SELECT
        trade_date      AS tradeDate,
        change_type      AS changeType,
        total_price      AS totalPrice,
        price             AS price,
        changer_name      AS changerName,
        changer_position AS changerPosition
        FROM daily_stock_change
        WHERE
        stock_code = #{code}
        ORDER BY trade_date
    </select>
</mapper>